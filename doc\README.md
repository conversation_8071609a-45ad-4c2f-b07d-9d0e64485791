# 警告系统项目文档

## 项目概览

警告系统（Warning System）是一个基于机器学习的智能预警平台，集成了前端Vue3应用和后端Spring Boot微服务架构。系统主要用于数据集管理、特征筛选、模型建立和预警事件处理。

## 项目结构

```
warning-system-pc/
├── src/                          # 前端Vue3项目源码
│   ├── api/                      # API接口调用
│   ├── assets/                   # 静态资源
│   ├── components/               # Vue组件
│   ├── pages/                    # 页面组件
│   ├── router/                   # 路由配置
│   ├── stores/                   # Pinia状态管理
│   └── i18n/                     # 国际化配置
├── alert-converge-api/           # 后端API模块
├── alert-converge-job/           # 定时任务模块
├── alert-converge-common/        # 通用工具模块
├── alert-converge-repository/    # 数据访问层模块
├── alert-converge-common-biz/    # 业务逻辑模块
├── alert-converge-env/           # 环境配置模块
└── doc/                          # 项目文档
```

## 技术栈

### 前端技术栈
- **Vue 3.2.47** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript超集
- **Vite 4.1.4** - 现代化构建工具
- **Element Plus 2.9.5** - Vue 3组件库
- **Pinia 2.0.32** - Vue状态管理库
- **Vue Router 4.1.6** - Vue官方路由管理器
- **Vue I18n 9.2.2** - 国际化插件
- **Axios 1.3.4** - HTTP客户端
- **Sass** - CSS预处理器

### 后端技术栈
- **Java 1.8** - 编程语言
- **Spring Boot** - 微服务框架
- **MyBatis Plus ********* - 数据库ORM框架
- **MySQL** - 关系型数据库
- **Redis** - 缓存数据库
- **XXL-Job** - 分布式任务调度
- **Knife4j** - API文档工具
- **Maven** - 项目构建工具

### 集成组件
- **structure-swagger** - 统一API文档服务
- **structure-redis** - Redis操作组件
- **structure-lock** - 分布式锁服务
- **structure-http** - 网络操作组件
- **structure-notice** - 钉钉预警组件
- **structure-log** - 日志记录组件
- **structure-secure** - 接口授权服务

## 核心功能模块

### 1. 用户认证与授权
- SSO单点登录集成
- 基于Token的身份验证
- 权限菜单管理
- 用户信息管理

### 2. 数据集中心
- 数据源配置管理
- 数据集创建和管理
- 数据集详情查看
- 数据导入导出

### 3. 特征筛选中心
- 特征筛选算法
- 特征查看和分析
- 特征结果展示

### 4. 建模中心
- 机器学习模型构建
- 模型训练和评估
- 报告中心
- 模型审核流程
- Jupyter Notebook集成

### 5. 预警系统
- 预警模型管理
- 预警事件处理
- 实时预警通知
- 预警数据统计

## 环境要求

### 开发环境
- **Node.js**: 16.0.0+
- **npm**: 7.10.0+
- **Java**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+
- **Redis**: 6.0+

### 推荐开发工具
- **前端**: VS Code + Volar插件
- **后端**: IntelliJ IDEA
- **数据库**: Navicat或DataGrip

## 快速开始

### 前端启动

```bash
# 安装依赖
npm install

# 开发环境启动
npm run dev

# 测试环境构建
npm run build:test

# 生产环境构建
npm run build:pro
```

### 后端启动

```bash
# 编译项目
mvn clean compile

# 启动API服务
cd alert-converge-api
mvn spring-boot:run

# 启动定时任务服务
cd alert-converge-job
mvn spring-boot:run
```

## 配置说明

### 前端配置
- **开发环境**: `vite.config.ts`中配置代理
- **全局设置**: `src/globalSettings.ts`
- **环境变量**: `.env`文件

### 后端配置
- **应用配置**: `application.yml`
- **环境配置**: `application-{profile}.yml`
- **数据库配置**: 在环境配置文件中设置
- **Redis配置**: 在环境配置文件中设置

## 部署说明

### 前端部署
1. 执行构建命令生成dist目录
2. 将dist目录部署到Web服务器（如Nginx）
3. 配置反向代理到后端服务

### 后端部署
1. 使用Maven打包生成jar文件
2. 配置生产环境数据库和Redis
3. 使用java -jar命令启动服务
4. 配置负载均衡和监控

## 文档导航

- [系统架构设计](./architecture.md)
- [数据库设计](./database-design.md)
- [API接口文档](./api-documentation.md)
- [部署运维指南](./deployment-guide.md)
- [功能需求文档](./requirements.md)
- [技术设计文档](./technical-design.md)
- [前端开发指南](./frontend-guide.md)
- [常见问题解答](./troubleshooting.md)

## 联系方式

如有问题或建议，请联系开发团队。

---

*最后更新时间: 2025-01-04*
