# 存放【dev】独享配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: HZEDHikariCP
      minimum-idle: 2
      maximum-pool-size: 10
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
    url: *************************************************************************************************************************************************************************************
    username: u_alertdb_dev
    password: MJci8812hhGGjlksahgFG
  redis:
    database: 237
    host: redis-local.qmwallet.vip
    port: 4467
    password: smu72fjs9bbshzp
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        min-idle: 1
        max-idle: 8
        max-wait: 3000ms
        shutdown-timeout: 5000ms
      cluster:
        refresh:
          adaptive: true
          period: 10000ms


hzed:
  sso:
    filter-chain-definition-map[/**/**]: anon

# jetcache
jetcache:
  statIntervalMinutes: 0
  areaInCacheName: false
  hidePackages: com.hzed.pub
  local:
    default:
      type: caffeine
      limit: 5000
      keyConvertor: fastjson2
      expireAfterWriteInMillis: 100000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson2
      valueEncoder: bean:jetJacksonValueEncoder
      valueDecoder: bean:jetJacksonValueDecoder
      poolConfig:
        minIdle: 1
        maxIdle: 8
        maxTotal: 8
      uri: redis://<EMAIL>:4467/0?timeout=5s

# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    enabled: true

# sql打印
pub:
  mybatis-plus:
    show-sql-log: true